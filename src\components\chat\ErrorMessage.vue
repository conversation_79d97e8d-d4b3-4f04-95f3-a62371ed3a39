<template>
  <div class="error-message">
    <div class="error-content">
      <div class="error-icon">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
          <line x1="15" y1="9" x2="9" y2="15" stroke="#ef4444" stroke-width="2"/>
          <line x1="9" y1="9" x2="15" y2="15" stroke="#ef4444" stroke-width="2"/>
        </svg>
      </div>
      <div class="error-text">
        <div class="error-title">发送消息失败</div>
        <div class="error-details">{{ errorMessage }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  errorMessage: string;
}>();
</script>

<style lang="scss" scoped>
.error-message {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  margin: 8px auto;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  max-width: 600px;
  align-self: center;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.error-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.error-text {
  flex: 1;
}

.error-title {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 4px;
}

.error-details {
  color: #7f1d1d;
  font-size: 0.9em;
  line-height: 1.4;
  word-break: break-word;
}


</style>
