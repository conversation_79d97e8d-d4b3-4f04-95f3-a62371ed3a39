from flask import request, jsonify, current_app, Response
from flask_login import login_required, current_user
from app import db # 确保 db 实例已正确初始化
from app.models import Conversation, ChatMessageNode, SystemSetting # 导入模型
from app.chat import bp # 假设 bp = Blueprint('chat', __name__, url_prefix='/api')
from app.services.llm_service import get_llm_completion, get_llm_completion_stream # LLM 服务接口
# import uuid # uuid 已在 models.py 中用于 node_id 默认值
from datetime import datetime
import json

# 辅助函数：为 LLM 构建历史记录 (简化版)
def build_llm_history(chat_id: int):
    """
    从数据库中获取指定对话的所有消息，按时间顺序排列，构建 LLM 需要的 {role, content} 列表。
    """
    history_for_llm = []

    # 获取系统提示
    system_prompt_setting = SystemSetting.query.filter_by(key='system_prompt').first()
    system_prompt_content = system_prompt_setting.value if system_prompt_setting else "You are a helpful assistant."
    history_for_llm.append({"role": "system", "content": system_prompt_content})

    # 获取该对话的所有消息，按时间戳升序排列
    # ChatMessageNode 在 Conversation.message_nodes relationship 中已定义 order_by
    # 或者直接查询并排序
    messages = ChatMessageNode.query.filter_by(conversation_id=chat_id)\
                                    .order_by(ChatMessageNode.timestamp.asc()).all()

    for msg_node in messages:
        history_for_llm.append({'role': msg_node.role, 'content': msg_node.content})

    return history_for_llm


@bp.route('/chats', methods=['POST'])
@login_required
def create_chat_session():
    data = request.get_json()
    title = data.get('title', 'New Chat')
    # entry_node_id 不再需要
    conversation = Conversation(user_id=current_user.id, title=title)
    db.session.add(conversation)
    db.session.commit()
    response_data = conversation.to_dict_metadata() # 使用 to_dict_metadata 更合适，因为此时还没有消息
    return jsonify(response_data), 201

@bp.route('/chats', methods=['GET'])
@login_required
def get_chat_sessions_list():
    conversations = Conversation.query.filter_by(user_id=current_user.id).order_by(Conversation.updated_at.desc()).all()
    return jsonify([c.to_dict_metadata() for c in conversations]), 200

@bp.route('/chats/<string:chat_id_str>', methods=['GET'])
@login_required
def get_chat_session_details(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    # to_dict_full() 将返回包含排序后消息的完整对话数据
    return jsonify(conversation.to_dict_full()), 200

@bp.route('/chats/<string:chat_id_str>', methods=['DELETE'])
@login_required
def delete_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    db.session.delete(conversation) #级联删除将处理 ChatMessageNodes
    db.session.commit()
    return jsonify({'message': 'Conversation deleted'}), 200

@bp.route('/chats/<string:chat_id_str>/rename', methods=['PUT'])
@login_required
def rename_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()
    new_title = data.get('title')
    if not new_title or len(new_title.strip()) == 0 or len(new_title) > 150 :
        return jsonify({'message': 'Valid title is required (1-150 chars)'}), 400

    conversation.title = new_title
    conversation.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify({'message': 'Conversation title updated', 'id': chat_id_str, 'new_title': new_title}), 200

@bp.route('/chats/<string:chat_id_str>/message_turns', methods=['POST'])
@login_required
def post_message_turn(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()

    user_content = data.get('user_content')
    if not user_content:
        return jsonify({'message': "user_content is required"}), 400

    # 1. 创建用户消息节点
    current_user_node = ChatMessageNode(
        conversation_id=chat_id,
        role='user',
        content=user_content,
        message_metadata=data.get('metadata', {}),
        timestamp=datetime.utcnow()
    )
    db.session.add(current_user_node)
    db.session.flush() # 确保 user_node 对后续的 build_llm_history 可见，并分配了 node_id 和 timestamp

    new_user_node_data = current_user_node.to_dict()

    # 2. 构建历史并调用 LLM 服务
    llm_history = build_llm_history(chat_id) # 此函数现在会获取包括刚添加的 current_user_node 在内的所有历史

    try:
        assistant_content = get_llm_completion(llm_history)
        if assistant_content is None or (isinstance(assistant_content, str) and "Error:" in assistant_content): # 粗略错误检查
             raise Exception(f"LLM service error: {assistant_content}")
    except Exception as e:
        db.session.rollback() # 如果 LLM 调用失败，回滚用户消息的添加
        current_app.logger.error(f"Error getting response from LLM: {e}")
        return jsonify({'message': 'Error getting response from LLM', 'details': str(e)}), 500


    # 3. 创建助手消息节点
    current_assistant_node = ChatMessageNode(
        conversation_id=chat_id,
        role='assistant',
        content=assistant_content,
        message_metadata={}, # 可以添加 LLM 模型信息等
        timestamp=datetime.utcnow()
    )
    db.session.add(current_assistant_node)
    new_assistant_node_data = current_assistant_node.to_dict() # 在 commit 前 to_dict 以获取默认值

    # 更新对话时间戳
    conversation.updated_at = datetime.utcnow()
    db.session.add(conversation)

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Database commit error after LLM response: {e}")
        return jsonify({'message': 'Error saving messages to database', 'details': str(e)}), 500


    return jsonify({
        'new_user_node': new_user_node_data,
        'new_assistant_node': new_assistant_node_data,
        'chat_session_meta_update': {
            'id': chat_id_str,
            'updated_at': conversation.updated_at.isoformat() + 'Z',
        }
    }), 201

# /chats/<string:chat_id_str>/entry_node (set_entry_node) 路由已移除
# /chats/<string:chat_id_str>/nodes/<string:node_id>/set_active_branch (set_active_branch) 路由已移除

@bp.route('/chats/<string:chat_id_str>/message_turns/stream', methods=['POST'])
@login_required
def post_message_turn_stream(chat_id_str):
    """
    流式传输版本的消息发送端点，使用SSE (Server-Sent Events)
    """
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    # 验证对话存在且属于当前用户
    Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()

    user_content = data.get('user_content')
    if not user_content:
        return jsonify({'message': "user_content is required"}), 400

    # 在路由函数中保存应用上下文和必要的数据
    app = current_app._get_current_object()
    user_id = current_user.id

    def generate_stream():
        try:
            with app.app_context():
                # 重新获取conversation对象（因为跨上下文）
                conversation = Conversation.query.filter_by(id=chat_id, user_id=user_id).first()
                if not conversation:
                    yield f"data: {json.dumps({'type': 'error', 'data': {'message': 'Conversation not found'}})}\n\n"
                    return

                # 1. 创建用户消息节点
                current_user_node = ChatMessageNode(
                    conversation_id=chat_id,
                    role='user',
                    content=user_content,
                    message_metadata=data.get('metadata', {}),
                    timestamp=datetime.utcnow()
                )
                db.session.add(current_user_node)
                db.session.flush()  # 确保 user_node 对后续的 build_llm_history 可见

                new_user_node_data = current_user_node.to_dict()

                # 发送用户消息节点数据
                yield f"data: {json.dumps({'type': 'user_message', 'data': new_user_node_data})}\n\n"

                # 2. 构建历史并调用流式 LLM 服务
                llm_history = build_llm_history(chat_id)

                # 3. 创建助手消息节点（初始为空内容）
                current_assistant_node = ChatMessageNode(
                    conversation_id=chat_id,
                    role='assistant',
                    content='',  # 初始为空，将通过流式更新
                    message_metadata={},
                    timestamp=datetime.utcnow()
                )
                db.session.add(current_assistant_node)
                db.session.flush()  # 获取node_id

                # 发送初始助手消息节点
                assistant_node_data = current_assistant_node.to_dict()
                yield f"data: {json.dumps({'type': 'assistant_message_start', 'data': assistant_node_data})}\n\n"

                # 4. 流式获取LLM响应
                full_content = ""
                llm_error_occurred = False

                try:
                    for chunk in get_llm_completion_stream(llm_history):
                        if chunk and not chunk.startswith("Error:"):
                            full_content += chunk
                            # 发送内容块
                            yield f"data: {json.dumps({'type': 'content_chunk', 'data': {'node_id': current_assistant_node.node_id, 'chunk': chunk}})}\n\n"
                        elif chunk.startswith("Error:"):
                            # 发送错误信息
                            yield f"data: {json.dumps({'type': 'error', 'data': {'message': chunk}})}\n\n"
                            llm_error_occurred = True
                            break

                    if not llm_error_occurred:
                        # 5. 更新助手消息节点的完整内容
                        current_assistant_node.content = full_content

                        # 更新对话时间戳
                        conversation.updated_at = datetime.utcnow()
                        db.session.add(conversation)
                        db.session.commit()

                        # 发送完成信号和元数据更新
                        final_assistant_node_data = current_assistant_node.to_dict()
                        chat_meta_update = {
                            'id': chat_id_str,
                            'updated_at': conversation.updated_at.isoformat() + 'Z',
                        }

                        yield f"data: {json.dumps({'type': 'message_complete', 'data': {'assistant_node': final_assistant_node_data, 'chat_session_meta_update': chat_meta_update}})}\n\n"
                    else:
                        # LLM错误时回滚
                        db.session.rollback()

                except Exception as llm_error:
                    db.session.rollback()
                    current_app.logger.error(f"Error getting response from LLM: {llm_error}")
                    yield f"data: {json.dumps({'type': 'error', 'data': {'message': f'Error getting response from LLM: {str(llm_error)}'}})}\n\n"

        except Exception as e:
            # 在应用上下文外部的错误处理
            try:
                with app.app_context():
                    db.session.rollback()
                    current_app.logger.error(f"Error in stream generation: {e}")
            except Exception:
                pass  # 忽略回滚错误

            yield f"data: {json.dumps({'type': 'error', 'data': {'message': f'Error in stream generation: {str(e)}'}})}\n\n"

    return Response(
        generate_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@bp.route('/chats/<string:chat_id_str>/summarize', methods=['POST'])
@login_required
def summarize_conversation(chat_id_str):
    """
    总结对话并生成标题，只使用第一个用户消息和第一个LLM消息
    """
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()

    # 获取对话的前两条消息（第一个用户消息和第一个助手消息）
    messages = ChatMessageNode.query.filter_by(conversation_id=chat_id)\
                                    .order_by(ChatMessageNode.timestamp.asc())\
                                    .limit(2).all()

    # 检查是否有足够的消息进行总结
    if len(messages) < 2:
        return jsonify({'message': 'Not enough messages to summarize'}), 400

    first_user_msg = messages[0] if messages[0].role == 'user' else None
    first_assistant_msg = messages[1] if messages[1].role == 'assistant' else None

    if not first_user_msg or not first_assistant_msg:
        return jsonify({'message': 'Invalid message sequence for summarization'}), 400

    # 构建用于总结的提示
    summarize_prompt = [
        {
            "role": "system",
            "content": "你是一个专门用于生成对话标题的助手。请根据用户的第一个问题和助手的第一个回答，生成一个简洁、准确的中文标题，不超过20个字符。只返回标题，不要其他内容。"
        },
        {
            "role": "user",
            "content": f"用户问题：{first_user_msg.content}\n\n助手回答：{first_assistant_msg.content[:500]}..."  # 限制长度避免token过多
        }
    ]

    try:
        # 调用LLM生成标题
        title = get_llm_completion(summarize_prompt, temperature=0.3, max_tokens=50)
        if title is None or (isinstance(title, str) and "Error:" in title):
            raise Exception(f"LLM service error: {title}")

        # 清理标题，移除可能的引号和多余空格
        title = title.strip().strip('"').strip("'").strip()

        # 确保标题长度合理
        if len(title) > 50:
            title = title[:47] + "..."

        # 更新对话标题
        conversation.title = title
        conversation.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'message': 'Conversation title updated successfully',
            'chat_id': chat_id_str,
            'new_title': title
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error generating conversation title: {e}")
        return jsonify({'message': 'Error generating conversation title', 'details': str(e)}), 500