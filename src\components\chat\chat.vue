<template>
  <div class="chat-root" v-if="chatSession">
    <div class="title-bar">
      <h2 class="chat-title">{{ chatSession.title || '未命名对话' }}</h2>
    </div>
    <div class="messages-container" ref="messagesContainerRef">
      <div class="messages">
        <template v-for="message in displayedMessages" :key="message.node_id">
          <div class="message-item">
            <UserMessage
              v-if="message.role === 'user'"
              :message="message.content"
              :timestamp="message.timestamp"
              :node-id="message.node_id"
            />
            <LlmMessage
              v-else
              :message="message.content"
              :timestamp="message.timestamp"
              :node-id="message.node_id"
            />
          </div>
        </template>
        <div v-if="sendError" class="message-item">
          <ErrorMessage
            :error-message="sendError"
          />
        </div>
        <div v-if="chatStore.isCurrentlySending" class="loading-indicator">正在发送...</div>
      </div>
    </div>
    <div class="input-area">
      <ChatInputBox :is-sending="chatStore.isCurrentlySending" @send="handleSendMessage" />
    </div>
  </div>
  <div v-else-if="isLoadingChatDetails" class="loading-chat-details">正在加载对话...</div>
  <div v-else class="no-chat-selected">请选择或创建一个新的对话。</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import LlmMessage from './llm-message.vue'; // 假设存在
import UserMessage from './user-message.vue'; // 假设存在
import ErrorMessage from './ErrorMessage.vue';
import ChatInputBox from '@/components/chat-initial/ChatInputBox.vue';
import { chatApiService } from '@/services/apiService';
import { useChatStore } from '@/store/chat';
import type { ChatSession, ChatMessageNode, MessageTurnPayload, StreamEvent } from '@/types/chat';

const props = defineProps<{
  activeChatId: string | null;
}>();

const route = useRoute();
const router = useRouter();
const chatStore = useChatStore();

const chatSession = ref<ChatSession | null>(null);
const isLoadingChatDetails = ref(false); // 用于加载对话详情
const sendError = ref<string | null>(null); // 发送消息错误
const lastUserNodeId = ref<string | null>(null); // 保存最后创建的用户消息节点ID，用于错误时撤销

const messagesContainerRef = ref<HTMLElement | null>(null);


// displayedMessages 现在直接是 chatSession 中的消息列表
// 后端应确保 message_nodes 按时间戳升序排列
const displayedMessages = computed<ChatMessageNode[]>(() => {
  return chatSession.value?.message_nodes || [];
});

watch(() => props.activeChatId, async (newId) => {
  if (newId) {
    await loadChatDetails(newId);
  } else {
    chatSession.value = null;
  }
}, { immediate: true });

watch(chatSession, (newChat) => {
  if (newChat) {
    // 滚动到底部
    scrollToBottom();
  }
}, { deep: true });

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainerRef.value) {
      messagesContainerRef.value.scrollTop = messagesContainerRef.value.scrollHeight;
    }
  });
};

async function loadChatDetails(chatId: string) {
  isLoadingChatDetails.value = true;
  chatSession.value = null; // 清空旧对话数据
  try {
    const chatData = await chatApiService.getChatDetails(chatId);
    // 确保 message_nodes 是按时间戳排序的，如果后端没保证，前端排序
    chatData.message_nodes.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    chatSession.value = chatData;
    scrollToBottom();

    // 检查是否有初始消息需要发送
    const initialMessage = route.query.initialMessage as string;
    if (initialMessage && chatData.message_nodes.length === 0) {
      // 这是一个新创建的空对话，需要发送初始消息
      const isDeepThinkActive = route.query.isDeepThinkActive === 'true';
      const isWebSearchActive = route.query.isWebSearchActive === 'true';

      // 清除查询参数，避免重复发送
      router.replace({ path: route.path });

      // 发送初始消息
      await handleSendMessage({
        content: initialMessage,
        isDeepThinkActive,
        isWebSearchActive
      });
    }
  } catch (error) {
    console.error("Failed to load chat details:", error);
    chatStore.setError('加载对话详情失败');
    // 可以在UI上显示错误
  } finally {
    isLoadingChatDetails.value = false;
  }
}

async function handleSendMessage(messageData: { content: string; isDeepThinkActive: boolean; isWebSearchActive: boolean }) {
  if (!messageData.content.trim() || !chatSession.value) return;

  // 清除之前的错误
  sendError.value = null;
  chatStore.setSending(true);

  const payload: MessageTurnPayload = {
    user_content: messageData.content.trim(),
    metadata: {
      client_type: "web_vue_client",
      deep_think: messageData.isDeepThinkActive,
      web_search: messageData.isWebSearchActive
    } // 包含深度思考和网络搜索配置
  };

  // 使用流式传输

  try {
    await chatApiService.postMessageTurnStream(
      chatSession.value.id,
      payload,
      (event) => {
        handleStreamEvent(event);
      },
      (error) => {
        console.error("Stream error:", error);
        // 不再设置到全局store，而是设置到本地状态
        sendError.value = error.message;
        // 撤销最后创建的用户消息节点
        removeLastUserNode();
        chatStore.setSending(false);
        scrollToBottom();
      },
      () => {
        chatStore.setSending(false);
        scrollToBottom();
      }
    );
  } catch (error) {
    console.error("Failed to send message:", error);
    // 不再设置到全局store，而是设置到本地状态
    sendError.value = error instanceof Error ? error.message : '发送消息失败';
    // 撤销最后创建的用户消息节点
    removeLastUserNode();
    chatStore.setSending(false);
    scrollToBottom();
  }
}

// 撤销最后创建的用户消息节点和可能的空助手消息节点
function removeLastUserNode() {
  if (lastUserNodeId.value && chatSession.value) {
    const userNodeIndex = chatSession.value.message_nodes.findIndex(
      node => node.node_id === lastUserNodeId.value
    );

    if (userNodeIndex !== -1) {
      // 检查是否有紧跟在用户消息后面的空助手消息
      const nextNodeIndex = userNodeIndex + 1;
      const nextNode = chatSession.value.message_nodes[nextNodeIndex];

      // 如果下一个节点是助手消息且内容为空或只有很少内容，也删除它
      if (nextNode && nextNode.role === 'assistant' && (!nextNode.content || nextNode.content.trim().length < 10)) {
        // 删除助手消息节点
        chatSession.value.message_nodes.splice(nextNodeIndex, 1);
      }

      // 删除用户消息节点
      chatSession.value.message_nodes.splice(userNodeIndex, 1);

      // 触发响应式更新
      chatSession.value = { ...chatSession.value };
    }
    lastUserNodeId.value = null;
  }
}

// 处理第一轮对话完成，调用总结API生成标题
async function handleFirstConversationComplete() {
  if (!chatSession.value) return;

  try {
    const result = await chatApiService.summarizeChat(chatSession.value.id);

    // 更新本地对话标题
    chatSession.value.title = result.new_title;

    // 更新store中的对话列表标题，使用现有的updateChatMetadata方法
    chatStore.updateChatMetadata(chatSession.value.id, result.new_title, chatSession.value.updated_at);

    console.log('对话标题已更新:', result.new_title);
  } catch (error) {
    console.error('生成对话标题失败:', error);
    // 不显示错误给用户，因为这不是关键功能
  }
}

function handleStreamEvent(event: StreamEvent) {
  if (!chatSession.value) return;

  switch (event.type) {
    case 'user_message':
      // 添加用户消息到对话中
      chatSession.value.message_nodes.push(event.data);
      // 记录用户消息节点ID，用于错误时撤销
      lastUserNodeId.value = event.data.node_id;
      scrollToBottom();
      break;

    case 'assistant_message_start':
      // 添加初始的助手消息节点
      chatSession.value.message_nodes.push(event.data);
      scrollToBottom();
      break;

    case 'content_chunk':
      // 更新助手消息的内容
      const nodeToUpdate = chatSession.value.message_nodes.find(
        node => node.node_id === event.data.node_id
      );
      if (nodeToUpdate) {
        nodeToUpdate.content += event.data.chunk;
        // 触发响应式更新
        chatSession.value = { ...chatSession.value };
        scrollToBottom();
      }
      break;

    case 'message_complete':
      // 更新最终的助手消息和对话元数据
      const finalNode = chatSession.value.message_nodes.find(
        node => node.node_id === event.data.assistant_node.node_id
      );
      if (finalNode) {
        Object.assign(finalNode, event.data.assistant_node);
      }

      // 更新对话元数据
      chatSession.value.updated_at = event.data.chat_session_meta_update.updated_at;
      chatStore.updateChatListMeta(
        event.data.chat_session_meta_update.id,
        event.data.chat_session_meta_update.updated_at
      );

      // 消息发送成功，清除错误状态和用户节点ID
      sendError.value = null;
      lastUserNodeId.value = null;

      // 检查是否是第一轮对话，如果是则调用总结API生成标题
      if (chatSession.value.message_nodes.length === 2) {
        // 第一轮对话完成（一个用户消息 + 一个助手消息）
        handleFirstConversationComplete();
      }

      scrollToBottom();
      break;

    case 'error':
      console.error("Stream error:", event.data.message);
      // 不再设置到全局store，而是设置到本地状态
      sendError.value = event.data.message;
      scrollToBottom();
      break;
  }
}

// 以下函数不再需要，因为编辑、重新生成和版本切换功能已移除：
// - promptEditUserMessage
// - handleRegenerateAssistantMessage
// - handleVersionChange
// - getVersionInfo
// - findNodeById (如果只是为了遍历，displayedMessages 本身就是列表)

</script>

<style lang="scss" scoped>
.chat-root {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 70%;
}

.title-bar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.chat-title {
  margin: 0;
  font-size: 1.1em;
  font-weight: 500;
  color: #333;
  flex-grow: 1;
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px 0; // 上下留白
}

.messages {
  display: flex;
  flex-direction: column;
  margin: auto; // 水平居中
  width: 100%;
  max-width: 820px; // 内容最大宽度
  padding: 0 10px; // 左右内边距
}

.loading-indicator, .loading-chat-details, .no-chat-selected {
  padding: 20px;
  text-align: center;
  color: #777;
  font-size: 1.1em;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-area {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #ffffff;
}

.message-item {
  margin-bottom: 12px;
  // 子组件 UserMessage/LlmMessage 将负责自己的具体样式
}
</style>